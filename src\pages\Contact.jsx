import React, { useState } from 'react';
import { motion } from 'motion/react';
import { Mail, Phone, MapPin, Send, MessageCircle } from 'lucide-react';
import { Button } from '../components/ui/button';
import axios from 'axios';
import { toast } from 'react-toastify';


const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [status, setStatus] = useState({
    isSending: false,
    message: '',
    isError: false,
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };


  // Handle form submission here
  const handleSubmit = async (e) => {
    e.preventDefault();

    setStatus({
      isSending: true,
      message: 'Sending...',
      isError: false,
    });


    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL
      const response = await axios.post(backendUrl + "/contact", formData);
      if (response.status === 200) {
        setStatus({
          isSending: false,
          message: 'Message sent!',
          isError: false,
        });

        toast.success('Message sent!');

        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });

      } else {
        setStatus({
          isSending: false,
          message: 'Failed to send message. Please try again.',
          isError: true,
        });
      }
    }
    catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to send message. Please try again.';
      setStatus({
        isSending: false,
        message: errorMessage,
        isError: true,
      });
      toast.error(errorMessage);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email',
      content: '<EMAIL>',
      description: 'Send us an email anytime!'
    },
    // {
    //   icon: Phone,
    //   title: 'Phone',
    //   content: '+****************',
    //   description: 'Mon-Fri from 8am to 5pm'
    // },
    {
      icon: MapPin,
      title: 'Office',
      content: 'New York, USA',
      description: 'Come say hello at our office'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="mx-auto max-w-7xl px-4 pt-5 pb-16 sm:px-6 lg:px-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl font-medium tracking-wide text-foreground sm:text-5xl mb-4">
            Get in Touch
          </h1>
          <p className="text-lg text-muted-foreground tracking-wide max-w-2xl mx-auto">
            Have questions about HealthyMe AI? We'd love to hear from you.
            Send us a message and we'll respond as soon as possible.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-28 lg:gap-12">

                  
          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-1 row-start-[-1] lg:row-start-auto"
          >
            <div className="space-y-8 lg:mt-5">
              <div>
                <h2 className="text-4xl lg:text-2xl font-semibold text-foreground mb-4 text-center lg:text-left ">
                  Contact Info
                </h2>
                <p className="text-muted-foreground text-center text-lg tracking-wide lg:text-left ">
                  Reach out to us through any of these channels. We're here to help!
                </p>
              </div>

              <div className="space-y-6">
                {contactInfo.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                    className="flex items-start space-x-4 p-4 rounded-lg bg-card border border-border hover:shadow-md transition-shadow"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <item.icon className="w-5 h-5 text-primary" />
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium text-foreground">{item.title}</h3>
                      <p className="text-foreground font-medium">{item.content}</p>
                      <p className="text-sm text-muted-foreground">{item.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>


           {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-2 row-start-[-2] lg:row-start-auto"
          >
            <div className="border border-border rounded-lg p-4 md:p-8 shadow-sm bg-card">
              <div className="flex items-center space-x-2 mb-6">
                <MessageCircle className="w-6 h-6 text-primary" />
                <h2 className="text-2xl font-semibold text-foreground">Send us a message</h2>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6" autoComplete='off'>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
                      placeholder="Your full name"
                      autocomplete="new-password"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                      autoComplete='off'
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-foreground mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
                    placeholder="What's this about?"
                    autoComplete='off'
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors resize-none"
                    placeholder="Tell us more about your inquiry..."
                    autoComplete='off'
                  />
                </div>

                <Button
                  type="submit"
                  disabled={status.isSending}
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  <Send className="w-4 h-4 mr-2" />
                  {status.isSending ? 'Sending...' : 'Send Message'}
                </Button>

                {/* Display submission status message */}
                {/* {status.message && (
        <p style={{ color: status.isError ? 'red' : 'green' }}>
          {status.message}
        </p>
      )} */}
              </form>
            </div>
          </motion.div>


        </div>
      </div>
    </div>
  );
};

export default Contact;
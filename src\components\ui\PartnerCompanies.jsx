import companies from '../../assets/DataHouse/Companies';


const CompanyItem = ({ imageUrl, name }) => (
  <li className="flex flex-col items-center justify-center w-[12rem] md:w-[16rem] flex-shrink-0">
    <div className="flex flex-col items-center justify-center h-24">
       <img 
        src={imageUrl} 
        alt={name} 
        className="max-h-12 w-auto object-contain"
        // Fallback in case an image fails to load
        onError={(e) => { e.target.onerror = null; e.target.src = `https://placehold.co/150x50/EFEFEF/333333?text=${name.replace(/\s/g, '+')}`; }}
      />
      <span className="mt-4 text-md font-semibold text-gray-600">{name}</span>
    </div>
  </li>
);


const PartnerCompanies = () => {
  // The key to the infinite scroll is to duplicate the logo list.
  // The animation moves the entire list, and when the first half is off-screen,
  // it seamlessly jumps back to the beginning.
  const extendedCompanies = [...companies, ...companies];

  return (
    <div className="bg-gray-50 min-h-screen flex flex-col items-center justify-center font-sans">
        <style>
        {`
          /* Keyframe animation for the infinite scroll */
          @keyframes infinite-scroll {
            from { transform: translateX(0); }
            to { transform: translateX(-50%); }
          }
          /* Class to apply the animation */
          .animate-infinite-scroll {
            /* We double the logos, so we only need to translate by 50% to create a seamless loop */
            animation: infinite-scroll 60s linear infinite;
          }
        `}
      </style>
      <div className="w-full max-w-6xl mx-auto py-12">
        <h2 className="text-center text-2xl md:text-3xl font-bold text-gray-800 mb-8">
          Trusted by the world's most innovative companies
        </h2>

        <div
          className="w-full inline-flex flex-nowrap overflow-hidden 
                     /* This mask creates the fade-in and fade-out effect on the edges */
                     [mask-image:_linear-gradient(to_right,transparent_0,_black_128px,_black_calc(100%-128px),transparent_100%)]"
        >
          <ul className="flex items-center justify-center md:justify-start [&_li]:mx-4 animate-infinite-scroll">
            {extendedCompanies.map((logo, index) => (
              <CompanyItem key={index} imageUrl={logo.imageUrl} name={logo.name} />
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PartnerCompanies;

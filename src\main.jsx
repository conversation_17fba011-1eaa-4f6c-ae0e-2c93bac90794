import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import 'react-toastify/dist/ReactToastify.css'
// import { ThemeProvider } from './components/theme-provider.js'
import App from './App.jsx'
import { BrowserRouter } from 'react-router-dom'

createRoot(document.getElementById('root')).render(
  <StrictMode>
  <BrowserRouter>
    {/* <ThemeProvider > */}
      <App />
    {/* </ThemeProvider> */}
  </BrowserRouter>
  </StrictMode>,
)

import { But<PERSON> } from "@/components/ui/button";
import { NavLink } from "react-router-dom";

const CallToAction = () => {
  return (
    <div className="max-w-2xl mx-auto text-center px-6  my-10 pt-10">
      <h2 className="text-xl tracking-wide leading-relaxed mb-4">
       Ready to See Your Practice Thrive?
      </h2>
      <p className="text-muted-foreground text-lg tracking-wide leading-relaxed mb-8 xl:px-2">
       Our AI-powered platform helps you improve diagnostic accuracy, increase efficiency, and focus on what matters most—your patients. Discover the difference HealthyMe AI can make.
      </p>
      
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <NavLink to="/contact">
          <Button variant="default" size="default" className="tracking-widest rounded-sm scale-100 hover:scale-105 duration-300 uppercase">
            Get in Touch
          </Button>
        </NavLink>
        <NavLink to="/about">
          <Button variant="outline" size="default" className="tracking-widest rounded-sm scale-100 hover:scale-105 duration-300 uppercase">
          Learn More
        </Button>
        </NavLink>
      </div>
    </div>
  );
};

export default CallToAction;
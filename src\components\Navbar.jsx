import React, { useState, useEffect, useRef } from 'react'
import { ModeToggle } from './theme-mode/mode-toggle'
import { NavLink } from 'react-router-dom'
import {Button } from './ui/button'
import { nav } from 'motion/react-client'


const Navbar = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef(null)
  const buttonRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && !dropdownRef.current.contains(event.target) &&
        buttonRef.current && !buttonRef.current.contains(event.target)
      ) {
        setIsDropdownOpen(false)
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDropdownOpen])



  const activeLinkStyle = 'pb-1 underline-offset-22 underline text-[17px] font-medium tracking-wide  text-gray-800 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 decoration-2';
  
  const inactiveLinkStyle = 'font-medium tracking-wide text-[17px] text-gray-800 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 decoration-transparent';


  return (
    // The parent nav classes are fine as they are general layout classes.
    <nav className='w-screen bg-background fixed shadow-md border-b border-border top-0 left-0 right-0 z-50 h-[61px]'>
    <div className="  max-w-8xl container xl:px-[104px] mx-auto">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Center - Logo */}
        <div className="flex-1 ">
          <NavLink to="/" className="text-2xl  font-bold text-gray-800 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200">
            HealthyMe AI
          </NavLink>
        </div>

        <div className='hidden md:flex space-x-8 mr-10'>
          <NavLink to="/" className={({ isActive }) => isActive ? activeLinkStyle : inactiveLinkStyle}>
            Home
          </NavLink>
          <NavLink to="/about" className={({ isActive }) => isActive ? activeLinkStyle : inactiveLinkStyle}>
            About
          </NavLink>
          <NavLink to="/contact" className={({ isActive }) => isActive ? activeLinkStyle : inactiveLinkStyle}>
            Contact
          </NavLink>
        </div>


        {/* theme toggler */}
        {/* <div className="flex items-center space-x-2 mr-5 md:mr-0  text-black px-2 py-1 rounded-sm  hover:text-white transition-colors duration-400 ">
          <ModeToggle />
        </div> */}

        <NavLink to="/contact" className="hidden xxs:block">
                            <Button variant="default" size="default" className="tracking-widest rounded-sm scale-100 hover:scale-105 duration-300">
                                LET'S TALK
                            </Button>
                        </NavLink>


        {/* right side - Mobile menu */}
        <div className="ml-3 md:hidden flex items-center">
          <div className="relative">
            <button
              ref={buttonRef}
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-600 dark:text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            </button>

            {/* Dropdown menu */}
            {isDropdownOpen && (
              // --- KEY CHANGES ARE HERE ---
              <div ref={dropdownRef}
                // Use popover for background and a standard border
                className="absolute -left-32 h-screen top-10  mt-2 w-52 bg-popover text-popover-foreground rounded-lg shadow-lg border z-50"
              >
                <div className="p-2 ">
                  <NavLink
                    to="/"
                    // Use accent for hover and ensure text color changes on hover too
                    className="block px-4 py-2 rounded-sm font-semibold text-md hover:bg-accent hover:text-accent-foreground transition-colors duration-200"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    Home
                  </NavLink>
                  <NavLink
                    to="/about"
                    className="block px-4 py-2 rounded-sm font-semibold text-md hover:bg-accent hover:text-accent-foreground transition-colors duration-200"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    About
                  </NavLink>
                  <NavLink
                    to="/contact"
                    className="block px-4 py-2 rounded-sm font-semibold text-md hover:bg-accent hover:text-accent-foreground transition-colors duration-200"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    Contact
                  </NavLink>
                </div>
              </div>
            )}
          </div>
        </div>




      </div>
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40 bg-opacity-25 md:hidden"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
    </nav>
  )
}

export default Navbar


import { Instagram, Linkedin } from 'lucide-react'

const TeamCard = ({team}) => {
  return (
    // <div className=' w-full lg:w-5xl lg:mx-auto rounded-lg border border-border bg-card p-3 md:p-6 flex gap-4 md:gap-6  flex-wrap sm:flex-nowrap hover:shadow-md transition-shadow duration-300'>
    <div className='w-full  mx-auto lg:w-[435px] xl:w-lg rounded-xl border border-border  p-3 md:p-6  gap-4 md:gap-6 bg-gray-50   shadow-sm hover:shadow-lg transition-shadow duration-300'>
        <div className='flex-shrink-0 '>
            <img
              // className='w-28 h-28 sm:w-40 sm:h-40 rounded-lg object-cover border-2 border-border'
              className='w-full xs:h-48 xs:w-48 rounded-lg object-cover border-2 border-border mx-auto'
              src={team.image}
              alt={team.title}
            />
        </div>
        <div className='flex-1 min-w-0'>
            <h3 className='text-2xl tracking-wide text-foreground mb-2 leading-tight text-center pt-5'>{team.title}</h3>
            <p className=' text-center text-xl tracking-wide mb-3 text-gray-800'>{team.jobTitle}</p>
            <p className='text-muted-foreground  mb-5 text-center text-lg tracking-wide py-2'>{team.description}</p>
            <div className='flex justify-center gap-10'>
              {team.linkedin && (
              <a
                href={team.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex gap-2 text-primary hover:text-primary/80 transition-colors "
              >
                <Linkedin className="w-4 h-4" />
                <span className="text-sm font-medium">LinkedIn</span>
              </a>
            )}
             <a
                href={team.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex gap-2 text-primary hover:text-primary/80 transition-colors "
              >
                <Instagram className="w-4 h-4" />
                <span className="text-sm font-medium">Instagram</span>
              </a>
            </div>
        </div>
    </div>
  )
}

export default TeamCard

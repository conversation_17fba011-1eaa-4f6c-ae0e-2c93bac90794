import React from 'react'
import { motion } from 'motion/react'
import TeamCard from '../components/TeamCard'
import { teamData } from '../assets/DataHouse/Teams'
import Heading from '../components/ui/Heading'
import CoreValueCard from '../components/ui/CoreValueCard'
import { coreValues } from '../assets/DataHouse/CoreValues'
import image from '../assets/images/aboutHero1.webp'



const About = () => {
  return (<>
    <div className='my-10 mx-auto max-w-7xl'>
      </div>
    <div className='my-10 mx-auto max-w-7xl'>


     

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
         <div className="relative flex flex-col items-center mx-auto lg:flex-row-reverse lg:max-w-5xl lg:mt-12 xl:max-w-6xl">
        <div className="w-full h-64 lg:w-1/2 lg:h-auto ">
          <img className="h-full w-full object-cover rounded-lg opacity-[.9]" src={image} alt="Winding mountain road" />
        </div>
        <div
          className="max-w-lg rounded-md bg-white/90 md:max-w-2xl md:z-10 md:shadow-lg md:absolute md:top-0 md:mt-48 lg:w-3/5 lg:left-0 lg:mt-20 lg:ml-20 xl:mt-24 xl:ml-12">
          <div className="flex flex-col py-12  md:px-16">
            <h2 className="text-[26px] font-medium tracking-wider  text-center md:text-left  lg:text-[26px]">
              Founded by Clinicians, for Clinicians.
            </h2>
            <p className="mt-4 text-lg tracking-wide  text-gray-600 text-justify dark:text-gray-300">
              HealthyMe AI was founded by a board-certified dermatologist to solve the real-world challenges clinicians face every day. We empower dermatology practices with a powerful AI co-pilot designed to improve diagnostic accuracy, standardize care across providers, and unlock new levels of practice potential. Guided by core values of integrity and empathy, our technology is engineered to support, not replace, the critical judgment of healthcare professionals and enhance the quality of patient care.
            </p>
           
          </div>
        </div>
      </div>
      </motion.div>


     <section className='mt-28 md:mt-80 lg:mt-28 '>
       {/* Header with subtle fade-in */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className='text-2xl font-bold my-10'
      >
        <Heading text={"Leadership Team"} />
      </motion.div>

      {/* Team cards with staggered animation */}
      {/* <div className='grid grid-cols-1 gap-10 mt-20'> */}
      <div className='flex justify-around flex-wrap lg:flex-nowrap gap-10 mt-10'>
        {teamData.map((team, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: index * 0.1,
              ease: "easeOut"
            }}
          >
            <TeamCard team={team} />
          </motion.div>
        ))}
      </div>
     </section>



      {/* Our Core Values Section */}
      <section className='mt-40'>
        <Heading text={"Our Core Values"} />

        <div className="grid grid-cols-1 max-w-6xl gap-10 mx-auto mt-12 md:mt-24 mb-20 lg:px-10">


          {coreValues.map((value, index) => (
            <CoreValueCard key={index} number={index} title={value.title} description={value.description} image={value.image} />
          )) }



         

        </div>

      </section>
    </div>
    </>
  )
}

export default About
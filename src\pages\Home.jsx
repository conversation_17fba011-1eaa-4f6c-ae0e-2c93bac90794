import { motion } from "motion/react";
// import { useTheme } from "../components/theme-provider";
import heroBgImage from "../assets/images/herobg.webp";
import Heading from "../components/ui/Heading";
import { Dermatology } from "../assets/DataHouse/Dermatology";
import { MissionData } from "../assets/DataHouse/Mission";
import DermCard from "../components/ui/DermCard";
import MissionCard from "../components/ui/MissionCard";
import CallToAction from "../components/ui/CallToAction";
import { NavLink } from "react-router-dom";
import { Button } from "../components/ui/button";
import PartnerCompanies from "../components/ui/PartnerCompanies";


export default function Home() {

    return (
        <>
        <img
                    src={heroBgImage}
                    alt="Hero Background"
                    className="absolute  inset-0 h-screen w-full object-cover rounded-md opacity-[.3]  blur-xs"
                />
            <div className="relative mb-20 mx-auto flex max-w-7xl flex-col items-center justify-center rounded-md  md:min-h-[90vh] lg:h-[75vh]">
                
                <div className="px-4 py-10 md:py-10 ">
                    <h1 className="font-baskerville relative z-10 mx-auto md:mb-2 max-w-5xl text-center text-[35px] xxs:text-[40px] md:text-5xl font-semibold leading-normal md:leading-16  md:tracking-wide lg:text-6xl">
                        {"Revolutionizing Dermatology with AI-Powered Diagnosis"
                            .split(" ")
                            .map((word, index) => (
                                <motion.span
                                    key={index}
                                    initial={{ opacity: 0, filter: "blur(4px)", y: 10 }}
                                    animate={{ opacity: 1, filter: "blur(0px)", y: 0 }}
                                    transition={{
                                        duration: 0.6,
                                        delay: index * 0.1,
                                        ease: "easeInOut",
                                    }}
                                    className="mr-3 md:pb-6 inline-block bg-clip-text text-transparent bg-gradient-to-t dark:bg-gradient-to-b from-primary via-primary to-primary/50 dark:from-primary dark:via-primary dark:to-primary/10"
                                >
                                    {word}
                                </motion.span>
                            ))}
                    </h1>
                    <motion.p
                        initial={{
                            opacity: 0,
                            y: 20
                        }}
                        animate={{
                            opacity: 1,
                            y: 0
                        }}
                        transition={{
                            duration: 0.6,
                            delay: 0.4,
                            ease: "easeOut"
                        }}
                        className="relative z-10 mx-auto max-w-2xl mt-4 text-center text-lg tracking-wide md:text-xl text-muted-foreground leading-relaxed"
                    >
                        Empowering clinicians with AI-driven tools to improve diagnostic accuracy, standardize care, and unlock practice potential while improving patient care.
                    </motion.p>

                </div>
                <motion.div
                    initial={{
                        opacity: 0,
                        y: 20
                    }}
                    animate={{
                        opacity: 1,
                        y: 0
                    }}
                    transition={{
                        duration: 0.6,
                        delay: 0.6,
                        ease: "easeOut"
                    }}
                    className="relative mt-5 sm:mt-10  mx-auto max-w-4xl  text-justify md:text-center text-lg md:text-md text-muted-foreground"
                >

                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <NavLink to="/contact">
                            <Button variant="default" size="default" className="tracking-widest rounded-sm scale-100 hover:scale-105 duration-300">
                                GET IN TOUCH
                            </Button>
                        </NavLink>

                    </div>

                </motion.div>

            </div>



            {/* Partner Companies */}

            <section>
                <Heading text={"Partner Companies"} />
                <PartnerCompanies />
            </section>

            {/* Our Mission Section */}
            <section className="pt-28 md:pt-20">
                <Heading text={"Our Mission"} />
                <p className="text-lg tracking-wide max-w-3xl text-gray-600 mx-auto mt-10  text-center md:text-lg">Empowering clinicians with AI-driven tools to improve diagnostic accuracy, standardize care, and unlock practice potential while improving patient care.
                </p>

                <div className="grid  grid-cols-1 max-w-6xl gap-10 mx-auto mt-12 md:mt-24 mb-20 lg:px-10">

                    {MissionData.map((mission, index) => (
                        <MissionCard key={index} number={index} title={mission.title} description={mission.description} image={mission.image} />
                    ))}


                  

                </div>

            </section>

            {/* <section className="pt-10">
                <Heading text={"The Experience Gap is a Revenue Gap"} />

                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-7xl mx-auto mt-12 md:mt-24 mb-32 px-2 sm:px-6 lg:px-10">

                    {Dermatology.map((derm, index) => (
                        <DermCard key={index} title={derm.title} description={derm.description} image={derm.image} />
                    ))}

                </div>
            </section> */}



            {/* Call to Action Section */}
            <section className="pb-20 w-full mx-auto">
                <CallToAction />
            </section>

        </>
    );
}




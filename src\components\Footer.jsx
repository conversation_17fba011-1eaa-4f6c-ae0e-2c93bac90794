import React from 'react';
import { HeartPulse, Mail, MapPin, Phone, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import { Link } from 'react-router-dom';

const Footer = () => {
    // --- Data for Links ---
    const quickLinks = [
        { name: 'Home', href: '/' },
        { name: 'About', href: '/about' },
        { name: 'Contact', href: '/contact' },
    ];

    // const supportLinks = [
    //     { name: 'FAQ', href: '#' },
    //     { name: 'Privacy Policy', href: '#' },
    //     { name: 'Terms of Service', href: '#' },
    // ];

    const socialLinks = [
        // { name: 'Facebook', href: '#', icon: <Facebook size={20} /> },
        // { name: 'Twitter', href: '#', icon: <Twitter size={20} /> },
        // { name: 'Instagram', href: '#', icon: <Instagram size={20} /> },
        { name: 'LinkedIn', href: 'https://www.linkedin.com/company/healthyme-ai', icon: <Linkedin size={20} /> },
    ];

    return (
        <footer className="bg-[oklch(var(--background))] text-[oklch(var(--foreground))] border-t-2 mx-auto max-w-7xl">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-16 pb-5">
                
                {/* Main Footer Content: 4-column layout on large screens */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                    
                    {/* Column 1: Brand and Socials */}
                    <div className="md:col-span-2 lg:col-span-1">
                        <a href="#" className="flex items-center space-x-2 mb-4">
                             {/* <HeartPulse size={30} className="text-[oklch(var(--primary))]" /> */}
                            <span className="text-2xl font-bold text-[oklch(var(--card-foreground))]">
                                HealthyMe AI
                            </span>
                        </a>
                        <p className="text-sm text-[oklch(var(--muted-foreground))] max-w-xs mb-6">
                            Advancing the medical industry by incorporating cutting-edge technology.
                        </p>
                        <div className="flex items-center space-x-2">
                            <a
                                    href="https://www.linkedin.com/company/healthyme-ai"
                                    target="_blank" rel="noopener noreferrer" 
                                    className="p-2 flex items-center  gap-2   hover:bg-[oklch(var(--accent))] text-[oklch(var(--muted-foreground))] hover:text-[oklch(var(--primary))] transition-colors"
                                >
                             <Linkedin className="w-4 h-4" />
                             <span className="text-sm font-medium">LinkedIn</span>
                                 
                                </a>
                        </div>
                    </div>

                    {/* Column 3: Support */}
                    <div className='hidden lg:block'>
                        <h3 className="font-semibold text-base text-[oklch(var(--card-foreground))] mb-4"></h3>
                        {/* <ul className="space-y-3">
                            {supportLinks.map((link) => (
                                <li key={link.name}>
                                    <a href={link.href} className="text-sm hover:text-[oklch(var(--primary))] transition-colors">
                                        {link.name}
                                    </a>
                                </li>
                            ))}
                        </ul> */}
                    </div>

                    {/* Column 2: Quick Links */}
                    <div>
                        <h3 className="font-semibold text-base text-[oklch(var(--card-foreground))] mb-4">Quick Links</h3>
                        <ul className="space-y-3">
                            {quickLinks.map((link) => (
                                <li key={link.name}>
                                    <a href={link.href} className="text-sm hover:text-[oklch(var(--primary))] transition-colors">
                                        {link.name}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </div>
                    
                    

                    {/* Column 4: Contact Information */}
                    <div>
                        <h3 className="font-semibold text-base text-[oklch(var(--card-foreground))] mb-4">Contact Us</h3>
                        <ul className="space-y-4">
                            <li className="flex items-start space-x-3">
                                <Mail size={20} className="text-[oklch(var(--primary))] mt-1 shrink-0" />
                                <a href="mailto:<EMAIL>" className="text-sm hover:text-[oklch(var(--primary))] transition-colors">
                                    <EMAIL>
                                </a>
                            </li>
                             {/* <li className="flex items-start space-x-3">
                                <Phone size={20} className="text-[oklch(var(--primary))] mt-1 shrink-0" />
                                <a href="tel:+11234567890" className="text-sm hover:text-[oklch(var(--primary))] transition-colors">
                                    +****************
                                </a>
                            </li> */}
                            <li className="flex items-start space-x-3">
                                <MapPin size={20} className="text-[oklch(var(--primary))] mt-1 shrink-0" />
                                <span className="text-sm">New York, USA</span>
                            </li>
                            
                        </ul>
                    </div>
                </div>

                {/* Bottom Bar: Copyright */}
                <div className="mt-16 pt-8 border-t border-[oklch(var(--border))] text-center">
                    <p className="text-sm text-[oklch(var(--muted-foreground))]">
                       Copyright © {new Date().getFullYear()} Healthyme AI Company Co.  All Rights Reserved.
                    </p>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
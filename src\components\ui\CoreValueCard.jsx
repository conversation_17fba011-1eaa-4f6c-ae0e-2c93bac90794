

const CoreValueCard = ({ number, title, description, image }) => {
  return (
     <div
      className={`shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-zinc-50 to-zinc-100  border-gray-100 flex justify-between gap-10 border rounded-lg p-3 md:p-5 sm:border-0 
    ${number === 1
          ? 'flex-col-reverse md:flex-row-reverse'
          : 'flex-col-reverse md:flex-row'
        }
  `}
    >
      {/* Text Section - Will be on the left/top when number is 1 */}
      <div className="w-full md:w-1/2 flex flex-col ">
        <h2 className="mb-2 text-xl font-medium tracking-wide text-gray-900 dark:text-white">
          {title}
        </h2>
        <p className="mb-3 tracking-wide text-lg text-gray-600 dark:text-gray-400">
          {description}
        </p>
      </div>

      {/* Image Section - Will be on the right/bottom when number is 1 */}
      <div className="w-full md:w-1/2 flex justify-center items-center ">
        <img
          src={image}
          alt={title}
          className="w-full h-48 sm:h-52 md:h-56 object-cover rounded-md"
        />
      </div>
    </div>

  )
}

export default CoreValueCard